import {
  getPlayerStats as getLocalPlayerStats,
  savePlayerStats as updateLocalPlayerStats,
  updateStatsAfterGame as updateLocalStatsAfterGame,
  updateStatsAfterAbandonment as updateLocalStatsAfterAbandonment,
  type PlayerStats,
  type GameResult,
} from "./localStatsService";
import { calculateGameXp, calculateLevelFromXp } from "./experienceSystem";
import { getCurrentUser, type AuthUser } from "./authService";
import { supabase } from "@/integrations/supabase/client";
import { queueStatsUpdate } from "./statsQueue";
import { debugSession } from "@/utils/sessionDebug";

// 🔄 FUNZIONE HELPER PER RETRY OPERAZIONI DATABASE
const retryDatabaseOperation = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: any;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      console.warn(
        `⚠️ Tentativo DB ${attempt + 1}/${maxRetries} fallito:`,
        error
      );

      if (attempt < maxRetries - 1) {
        // Backoff exponential: 1s, 2s, 4s
        const delay = baseDelay * Math.pow(2, attempt);
        console.log(`⏳ Attesa ${delay}ms prima del prossimo tentativo...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
};

// 🔥 VERSIONE SEMPLIFICATA: Solo le funzioni essenziali

export interface ProfileData {
  stats: PlayerStats;
  isOnline: boolean;
  userId?: string;
  username?: string;
  customName?: string;
  lastSyncAt?: string;
  createdAt?: string;
  lastActive?: string;
}

// 🎯 CACHE SEMPLIFICATO
import { CACHE_TTL } from "@/utils/cache/simpleCacheHelper";

let profileCache: ProfileData | null = null;
let cacheTimestamp = 0;
let cacheUserId: string | null = null;
const CACHE_DURATION = CACHE_TTL.MEDIUM; // 5 minuti - più lungo per ridurre chiamate API

// Invalida la cache quando le statistiche vengono aggiornate
export const invalidateProfileCache = () => {
  profileCache = null;
  cacheTimestamp = 0;
  cacheUserId = null;
};

// Forza l'aggiornamento della cache con nuovi dati
export const forceUpdateProfileCache = (
  newStats: PlayerStats,
  username?: string
) => {
  if (profileCache && cacheUserId) {
    profileCache.stats = newStats;
    if (username) {
      profileCache.username = username;
    }
    cacheTimestamp = Date.now(); // Rinnova timestamp
    console.log(
      "🔄 Cache aggiornata:",
      username ? `username: ${username}` : "solo stats"
    );
  }
};

// Funzione specifica per aggiornare solo l'username nella cache
export const updateUsernameInCache = (newUsername: string) => {
  if (profileCache && cacheUserId) {
    profileCache.username = newUsername;
    cacheTimestamp = Date.now(); // Rinnova timestamp
    console.log("� Username aggiornato in cache:", newUsername);
  }
};

// Controlla se la cache è valida
const isCacheValid = (userId: string | null): boolean => {
  const now = Date.now();
  return (
    profileCache !== null &&
    cacheTimestamp > 0 &&
    now - cacheTimestamp < CACHE_DURATION &&
    cacheUserId === userId
  );
};

// Cache utente rimosso - usiamo direttamente supabase.auth.getUser()

// 🔧 Funzione per calcolare aggiornamento statistiche senza modificare i dati originali
const calculateStatsUpdate = (
  currentStats: PlayerStats,
  gameData: {
    isWinner: boolean;
    difficulty: "easy" | "medium" | "hard";
    playerTeam: number;
    finalScore: [number, number];
    maraffeMade?: number;
    isPerfectGame?: boolean;
    isComeback?: boolean;
    isDominantWin?: boolean;
    isAbandoned?: boolean;
  }
): {
  stats: PlayerStats;
  leveledUp: boolean;
  xpGained: number;
  xpBreakdown: string[];
} => {
  const stats = { ...currentStats }; // Copia delle statistiche correnti
  const oldLevel = stats.level;
  const now = new Date().toISOString();

  // Calcola XP
  let xpResult;
  if (gameData.isAbandoned) {
    xpResult = {
      totalXp: 0,
      breakdown: ["Partita abbandonata: 0 XP"],
    };
  } else {
    xpResult = calculateGameXp({
      isWinner: gameData.isWinner,
      difficulty: gameData.difficulty,
      maraffeMade: gameData.maraffeMade,
      isPerfectGame: gameData.isPerfectGame,
      isComeback: gameData.isComeback,
      isDominantWin: gameData.isDominantWin,
      isFirstWinOfDay: false, // Per ora non tracciamo questo online
      currentWinStreak: stats.currentWinStreak,
    });
  }

  // Aggiorna statistiche
  stats.xp += xpResult.totalXp;
  stats.totalGames += 1;
  stats.lastPlayed = now;

  // Aggiorna vittorie/sconfitte
  const isActualWinner = gameData.isWinner && !gameData.isAbandoned;
  if (isActualWinner) {
    stats.gamesWon += 1;
    stats.currentWinStreak += 1;
    stats.bestWinStreak = Math.max(stats.bestWinStreak, stats.currentWinStreak);
  } else {
    stats.gamesLost += 1;
    stats.currentWinStreak = 0;
  }

  // Calcola win rate
  stats.winRate =
    stats.totalGames > 0
      ? Math.round((stats.gamesWon / stats.totalGames) * 100)
      : 0;

  // Aggiorna maraffe
  if (gameData.maraffeMade) {
    stats.maraffeMade += gameData.maraffeMade;
  }

  // Calcola nuovo livello
  stats.level = calculateLevelFromXp(stats.xp);
  const leveledUp = stats.level > oldLevel;

  // Aggiorna recent games (solo per la cache locale)
  const gameResult: GameResult = {
    id: `game_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    date: now,
    result: gameData.isAbandoned
      ? "Sconfitta"
      : gameData.isWinner
      ? "Vittoria"
      : "Sconfitta",
    score: gameData.isAbandoned
      ? "Abbandonata"
      : `${gameData.finalScore[gameData.playerTeam]}-${
          gameData.finalScore[gameData.playerTeam === 0 ? 1 : 0]
        }`,
    difficulty: gameData.difficulty,
    playerTeam: gameData.playerTeam,
    xpGained: xpResult.totalXp,
    xpBreakdown: xpResult.breakdown,
  };

  // Aggiorna recent games (inizializza se non esiste)
  if (!stats.recentGames) {
    stats.recentGames = [];
  }

  if (stats.recentGames.length >= 5) {
    stats.recentGames.pop();
  }
  stats.recentGames.unshift(gameResult);

  stats.updatedAt = now;

  return {
    stats,
    leveledUp,
    xpGained: xpResult.totalXp,
    xpBreakdown: xpResult.breakdown,
  };
};

// 🎯 FUNZIONE PER GESTIRE DATE RETROCOMPATIBILI
const getReliableCreatedAt = (
  dbCreatedAt: string | undefined | null,
  gamesPlayed: number,
  fallbackDate: string
): string | undefined => {
  // Se non c'è data dal DB, restituisce undefined per mostrare "Non disponibile"
  if (!dbCreatedAt) {
    return undefined;
  }

  try {
    const createdDate = new Date(dbCreatedAt);
    const now = new Date();
    const daysDiff = Math.floor(
      (now.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    // Se la data è molto recente (meno di 30 giorni) ma l'utente ha molte partite,
    // probabilmente è un vecchio utente e la data non è affidabile
    if (daysDiff < 30 && gamesPlayed > 10) {
      console.log(
        `⚠️  Data sospetta per utente con ${gamesPlayed} partite (registrato ${daysDiff} giorni fa)`
      );
      return undefined; // Mostra "Non disponibile" invece di una data inaffidabile
    }

    return dbCreatedAt;
  } catch (error) {
    console.warn("Errore parsing data creazione:", error);
    return undefined;
  }
};

// 🔥 VERSIONE CON CACHE: Profilo che usa cache intelligente
export const getActiveProfile = async (): Promise<ProfileData> => {
  try {
    // 🎯 OTTIMIZZAZIONE: Controlla prima se abbiamo cache valida senza chiamare Supabase
    if (profileCache && cacheUserId && isCacheValid(cacheUserId)) {
      console.log("✅ Uso cache profilo valida, nessuna chiamata API");
      return profileCache;
    }

    // Solo ora fai la chiamata a Supabase se il cache non è valido
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return getLocalProfile();
    }

    // 🎯 USA LA CACHE se valida (doppio controllo per sicurezza)
    if (isCacheValid(user.id)) {
      return profileCache!;
    }

    // 🔄 SINCRONIZZAZIONE AL PRIMO ACCESSO: Controlla se serve sincronizzare
    // Se la cache non è valida o è la prima volta che accediamo, sincronizza
    if (!profileCache || cacheUserId !== user.id) {
      console.log("🔄 Prima sincronizzazione per utente:", user.id);
      const syncResult = await syncStatsOnLogin();

      if (syncResult.success && syncResult.conflicts) {
        console.log(
          "⚠️ Rilevati conflitti durante sincronizzazione:",
          syncResult.conflicts
        );
      }
    }

    // Solo stats, senza profile extra
    try {
      console.log("🔄 Caricamento profilo da Supabase");

      // 🎯 OTTIMIZZATO: Una singola query per ottenere stats + profilo completo
      const { data: statsData } = await supabase
        .from("game_stats")
        .select(
          `
          *,
          profiles!inner(username, created_at, updated_at)
        `
        )
        .eq("user_id", user.id)
        .single();

      console.log("📊 Dati ricevuti:", {
        username: (statsData as any)?.profiles?.username,
        level: (statsData as any)?.level,
        games: (statsData as any)?.games_played,
        created_at: (statsData as any)?.profiles?.created_at,
        updated_at: (statsData as any)?.profiles?.updated_at,
      });

      const localStats = getLocalPlayerStats();
      const onlineStats: PlayerStats = statsData
        ? {
            level: (statsData as any).level || 1,
            xp: (statsData as any).xp || 0,
            totalGames: (statsData as any).games_played || 0,
            gamesWon: (statsData as any).games_won || 0,
            gamesLost:
              ((statsData as any).games_played || 0) -
              ((statsData as any).games_won || 0),
            winRate:
              (statsData as any).games_played > 0
                ? Math.round(
                    ((statsData as any).games_won /
                      (statsData as any).games_played) *
                      100
                  )
                : 0,
            // Campi non salvati nel DB
            maraffeMade: localStats.maraffeMade || 0,
            achievementsUnlocked: localStats.achievementsUnlocked || [],
            lastPlayed:
              (statsData as any).updated_at || new Date().toISOString(),
            lastWinDate: localStats.lastWinDate,
            currentWinStreak: localStats.currentWinStreak || 0,
            bestWinStreak: localStats.bestWinStreak || 0,
            recentGames: localStats.recentGames || [],
            createdAt: (statsData as any).created_at || localStats.createdAt,
            updatedAt: (statsData as any).updated_at || localStats.updatedAt,
          }
        : localStats;

      // 🎯 CORRETTO: Estrazione username dalla tabella profiles
      let username = "Utente";
      if ((statsData as any)?.profiles?.username) {
        username = (statsData as any).profiles.username;
      } else if (user.email) {
        username = user.email.split("@")[0];
      }

      // 🎯 GESTIONE INTELLIGENTE DATE per retrocompatibilità
      const createdAt = getReliableCreatedAt(
        (statsData as any)?.profiles?.created_at,
        (statsData as any)?.games_played || 0,
        localStats.createdAt
      );

      const profileData: ProfileData = {
        stats: onlineStats,
        isOnline: true,
        userId: user.id,
        username: username,
        lastSyncAt: new Date().toISOString(),
        createdAt: createdAt,
        lastActive: statsData?.profiles?.updated_at || new Date().toISOString(),
      };

      // 🎯 SINCRONIZZA CON NUOVO SISTEMA STATISTICHE
      const { saveStats } = await import("./statsManager");
      const { updateUserProfile, saveUnifiedData } = await import(
        "./unifiedStorageService"
      );

      // Sincronizza statistiche con il nuovo sistema
      saveStats({
        level: onlineStats.level,
        xp: onlineStats.xp,
        gamesPlayed: onlineStats.totalGames,
        gamesWon: onlineStats.gamesWon,
        winRate: onlineStats.winRate,
        currentWinStreak: onlineStats.currentWinStreak,
        bestWinStreak: onlineStats.bestWinStreak,
        lastPlayed: onlineStats.lastPlayed,
      });

      updateUserProfile({
        username: username,
      });

      // 🎯 AGGIORNA MODALITÀ ONLINE
      saveUnifiedData({
        isOfflineMode: false, // L'utente è online se arriviamo qui
      });

      console.log("🔄 Username e modalità online sincronizzati:", username);

      // 🎯 SALVA IN CACHE
      profileCache = profileData;
      cacheTimestamp = Date.now();
      cacheUserId = user.id;
      console.log("💾 Profilo salvato in cache:", username);

      return profileData;
    } catch (error) {
      console.warn("Errore caricamento stats online, uso locale:", error);
      return getLocalProfile();
    }
  } catch (error) {
    console.error("Errore getActiveProfile:", error);
    return getLocalProfile();
  }
};

// Profilo locale di fallback
export const getLocalProfile = (): ProfileData => {
  const stats = getLocalPlayerStats();

  // Ottieni il titolo dinamico basato sul livello attuale
  const { getPlayerTitle } = require("./playerTitlesService");
  const playerTitle = getPlayerTitle(stats.level);

  return {
    stats,
    isOnline: false,
    customName: playerTitle.title,
  };
};

// 🔥 VERSIONE SEMPLIFICATA: Aggiornamento statistiche robusto
export const updateStatsAfterGame = async (gameData: {
  isWinner: boolean;
  difficulty: "easy" | "medium" | "hard";
  playerTeam: number;
  finalScore: [number, number];
  maraffeMade?: number;
  isPerfectGame?: boolean;
  isComeback?: boolean;
  isDominantWin?: boolean;
  isAbandoned?: boolean;
}): Promise<{
  stats: PlayerStats;
  leveledUp: boolean;
  xpGained: number;
  xpBreakdown: string[];
}> => {
  console.log("🎯 Aggiornamento statistiche:", gameData);
  console.log(
    "🔍 Stato iniziale - Online features:",
    typeof window !== "undefined" ? "browser" : "server"
  );

  // 1. Ottieni statistiche locali SUBITO (sempre funziona)
  const localStats = getLocalPlayerStats();
  const updatedStats = calculateStatsUpdate(localStats, gameData);

  console.log("📊 Statistiche aggiornate:", {
    level: updatedStats.stats.level,
    xp: updatedStats.stats.xp,
    totalGames: updatedStats.stats.totalGames,
    gamesWon: updatedStats.stats.gamesWon,
    xpGained: updatedStats.xpGained,
  });

  // 2. Salva SEMPRE localmente prima di tutto
  updateLocalPlayerStats(updatedStats.stats);

  // 🎯 SINCRONIZZA CON NUOVO SISTEMA STATISTICHE
  const { saveStats } = await import("./statsManager");
  saveStats({
    level: updatedStats.stats.level,
    xp: updatedStats.stats.xp,
    gamesPlayed: updatedStats.stats.totalGames,
    gamesWon: updatedStats.stats.gamesWon,
    winRate: updatedStats.stats.winRate,
    currentWinStreak: updatedStats.stats.currentWinStreak,
    bestWinStreak: updatedStats.stats.bestWinStreak,
    lastPlayed: updatedStats.stats.lastPlayed,
  });

  console.log("🔍 DEBUG: Prima di aggiornare cache profilo");

  // 🎯 AGGIORNA CACHE SUBITO con i nuovi dati (anche se salvataggio online fallisce)
  forceUpdateProfileCache(updatedStats.stats);

  console.log("🔍 DEBUG: Cache aggiornata, invio evento");

  window.dispatchEvent(new CustomEvent("statsUpdated"));

  console.log("🔍 DEBUG: Evento inviato, inizio salvataggio online");

  // 🚀 USA PATTERN FETCH DIRETTO CON CACHE UTENTE
  console.log("🚀 SALVATAGGIO STATISTICHE CON FETCH DIRETTO");

  try {
    console.log("� DEBUG: Controllo stato autenticazione per salvataggio");

    // 🔧 CONTROLLO ROBUSTO DELLA SESSIONE
    let user: any = null;
    let accessToken: string = "";

    console.log("🔍 DEBUG: Chiamata getSession...");
    // Prima prova con la sessione con timeout
    let sessionData: any = null;
    try {
      const sessionPromise = supabase.auth.getSession();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error("getSession timeout")), 3000)
      );

      const result = await Promise.race([sessionPromise, timeoutPromise]);
      sessionData = (result as any).data;
      console.log("🔍 DEBUG: getSession completata");
    } catch (sessionError) {
      console.error("❌ DEBUG: Errore getSession:", sessionError);

      // FALLBACK: Prova con getUser() con timeout ridotto
      console.log("🔄 DEBUG: Tentativo fallback con getUser...");
      try {
        const getUserPromise = supabase.auth.getUser();
        const getUserTimeout = new Promise((_, reject) =>
          setTimeout(() => reject(new Error("getUser timeout")), 2000)
        );

        const userResult = await Promise.race([getUserPromise, getUserTimeout]);
        const { data: userData, error: userError } = userResult as any;

        if (userData?.user && !userError) {
          console.log("✅ DEBUG: Fallback getUser riuscito");
          // Simula sessionData per compatibilità
          sessionData = {
            session: {
              user: userData.user,
              access_token: "fallback_token", // Token placeholder
            },
          };
        } else {
          console.error("❌ DEBUG: Anche getUser fallito:", userError);
          throw new Error("Sia getSession che getUser sono falliti");
        }
      } catch (userError) {
        console.error("❌ DEBUG: Errore getUser:", userError);

        // FALLBACK FINALE: Usa dati cached dell'utente se disponibili
        console.log("🚨 DEBUG: FALLBACK FINALE - Uso cache utente");
        const cachedUser = getCachedUserData();
        if (cachedUser) {
          console.log("✅ DEBUG: Trovati dati utente in cache");
          sessionData = {
            session: {
              user: cachedUser,
              access_token: "cached_token",
            },
          };
        } else {
          console.error("❌ DEBUG: Nessun dato utente in cache disponibile");

          // 🚨 ULTIMA RISORSA: FORCE SAVE senza autenticazione
          console.log(
            "🚨 DEBUG: FORCE SAVE - Salvataggio senza autenticazione"
          );
          await forceSaveStatsWithoutAuth(updatedStats.stats);
          return updatedStats; // Ritorna comunque le stats aggiornate
        }
      }
    }
    console.log("🔍 DEBUG: Dati sessione:", {
      hasSession: !!sessionData?.session,
      hasUser: !!sessionData?.session?.user,
      hasToken: !!sessionData?.session?.access_token,
      expiresAt: sessionData?.session?.expires_at,
    });

    if (sessionData?.session?.user && sessionData?.session?.access_token) {
      // Controlla se la sessione è scaduta
      const now = Math.floor(Date.now() / 1000);
      const expiresAt = sessionData.session.expires_at || 0;

      if (expiresAt > now) {
        user = sessionData.session.user;
        accessToken = sessionData.session.access_token;
        console.log("✅ DEBUG: Sessione valida, utente:", user.id);
      } else {
        console.log(
          "⚠️ DEBUG: Sessione scaduta, BYPASS REFRESH - FORZO SALVATAGGIO"
        );

        // 🚨 BYPASS COMPLETO: Prova prima con token esistente, poi forza
        user = sessionData.session.user;

        // Prova prima con il token esistente (anche se scaduto)
        accessToken = sessionData.session.access_token || "force_save_token";

        console.log(
          "🚨 FORCE SAVE: Usando token esistente (anche se scaduto), user:",
          user.id
        );
      }
    } else {
      console.log("⚠️ DEBUG: Nessuna sessione, provo getUser...");

      // Fallback: getUser
      const { data: userData, error: userError } =
        await supabase.auth.getUser();
      if (userError || !userData?.user) {
        console.log("❌ DEBUG: getUser fallito:", userError);
        throw new Error("Nessun utente autenticato");
      }

      // Se getUser funziona ma non abbiamo token, proviamo a ottenere una nuova sessione
      const { data: newSessionData } = await supabase.auth.getSession();
      if (newSessionData?.session?.access_token) {
        user = userData.user;
        accessToken = newSessionData.session.access_token;
        console.log("✅ DEBUG: Utente da getUser con nuova sessione:", user.id);
      } else {
        console.log("❌ DEBUG: Impossibile ottenere access token");
        throw new Error("Nessun access token disponibile");
      }
    }

    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;

    console.log("👤 Salvataggio online statistiche con fetch diretto...");

    // 🔒 PROTEZIONE: Leggi valori attuali prima di aggiornare (con fetch diretto)
    console.log("🔍 DEBUG: Tentativo lettura DB con user:", user.id);

    let fetchResponse;
    try {
      // 🚨 BYPASS COMPLETO: USA SOLO CHIAVE ANONIMA SENZA AUTHORIZATION
      const headers: Record<string, string> = {
        apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
        "Content-Type": "application/json",
      };

      // Se il token sembra valido (non è un placeholder), usalo
      if (
        accessToken &&
        accessToken !== "cached_token" &&
        accessToken !== "force_save_token" &&
        accessToken.length > 50
      ) {
        headers.Authorization = `Bearer ${accessToken}`;
        console.log("🔍 DEBUG: Usando token valido");
      } else {
        console.log("🚨 DEBUG: BYPASS AUTHORIZATION - Solo chiave anonima");
      }

      fetchResponse = await fetch(
        `${supabaseUrl}/rest/v1/game_stats?user_id=eq.${user.id}&select=games_played,games_won,level,xp`,
        {
          method: "GET",
          headers,
        }
      );

      console.log(
        "🔍 DEBUG: Risposta fetch GET:",
        fetchResponse.status,
        fetchResponse.statusText
      );
    } catch (fetchError) {
      console.error("❌ DEBUG: Errore fetch GET:", fetchError);
      throw new Error(`Fetch GET fallito: ${fetchError.message}`);
    }

    let currentDbStats = null;
    if (fetchResponse.ok) {
      const fetchData = await fetchResponse.json();
      currentDbStats = fetchData[0] || null;
    }

    // Calcola valori solo incrementali (mai decrementa)
    const currentGamesPlayed = currentDbStats?.games_played || 0;
    const currentGamesWon = currentDbStats?.games_won || 0;
    const currentLevel = currentDbStats?.level || 1;
    const currentXp = currentDbStats?.xp || 0;

    const newGamesPlayed = Math.max(
      currentGamesPlayed,
      updatedStats.stats.totalGames
    );
    const newGamesWon = Math.max(currentGamesWon, updatedStats.stats.gamesWon);
    const newLevel = Math.max(currentLevel, updatedStats.stats.level);
    const newXp = Math.max(currentXp, updatedStats.stats.xp);

    console.log("📊 Aggiornamento DB:", {
      games: `${currentGamesPlayed} → ${newGamesPlayed}`,
      wins: `${currentGamesWon} → ${newGamesWon}`,
      level: `${currentLevel} → ${newLevel}`,
      xp: `${currentXp} → ${newXp}`,
    });

    // 🔧 USA PATCH per evitare errore 409 duplicate key
    console.log("💾 DEBUG: Tentativo PATCH con dati:", {
      games_played: newGamesPlayed,
      games_won: newGamesWon,
      level: newLevel,
      xp: newXp,
    });

    let upsertResponse;
    try {
      // 🚨 STESSO BYPASS PER IL PATCH
      const patchHeaders: Record<string, string> = {
        apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
        "Content-Type": "application/json",
        Prefer: "return=minimal",
      };

      // Se il token sembra valido, usalo
      if (
        accessToken &&
        accessToken !== "cached_token" &&
        accessToken !== "force_save_token" &&
        accessToken.length > 50
      ) {
        patchHeaders.Authorization = `Bearer ${accessToken}`;
        console.log("💾 DEBUG: PATCH con token valido");
      } else {
        console.log(
          "🚨 DEBUG: PATCH BYPASS AUTHORIZATION - Solo chiave anonima"
        );
      }

      upsertResponse = await fetch(
        `${supabaseUrl}/rest/v1/game_stats?user_id=eq.${user.id}`,
        {
          method: "PATCH",
          headers: patchHeaders,
          body: JSON.stringify({
            games_played: newGamesPlayed,
            games_won: newGamesWon,
            level: newLevel,
            xp: newXp,
            updated_at: new Date().toISOString(),
          }),
        }
      );

      console.log(
        "💾 DEBUG: Risposta PATCH:",
        upsertResponse.status,
        upsertResponse.statusText
      );

      if (!upsertResponse.ok) {
        const errorText = await upsertResponse.text();
        console.error("❌ DEBUG: Errore PATCH:", errorText);
      }
    } catch (patchError) {
      console.error("❌ DEBUG: Errore fetch PATCH:", patchError);
      throw new Error(`Fetch PATCH fallito: ${patchError.message}`);
    }

    // Se PATCH fallisce (record non esiste), prova INSERT
    if (!upsertResponse.ok && upsertResponse.status === 404) {
      console.log("📊 Record non esiste, creo nuovo record...");
      // 🚨 STESSO BYPASS PER IL POST
      const postHeaders: Record<string, string> = {
        apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
        "Content-Type": "application/json",
        Prefer: "return=minimal",
      };

      // Se il token sembra valido, usalo
      if (
        accessToken &&
        accessToken !== "cached_token" &&
        accessToken !== "force_save_token" &&
        accessToken.length > 50
      ) {
        postHeaders.Authorization = `Bearer ${accessToken}`;
        console.log("📊 DEBUG: POST con token valido");
      } else {
        console.log(
          "🚨 DEBUG: POST BYPASS AUTHORIZATION - Solo chiave anonima"
        );
      }

      const insertResponse = await fetch(`${supabaseUrl}/rest/v1/game_stats`, {
        method: "POST",
        headers: postHeaders,
        body: JSON.stringify({
          user_id: user.id,
          games_played: newGamesPlayed,
          games_won: newGamesWon,
          level: newLevel,
          xp: newXp,
          updated_at: new Date().toISOString(),
        }),
      });

      if (!insertResponse.ok) {
        throw new Error(
          `HTTP ${insertResponse.status}: ${insertResponse.statusText}`
        );
      }
    } else if (!upsertResponse.ok) {
      throw new Error(
        `HTTP ${upsertResponse.status}: ${upsertResponse.statusText}`
      );
    }

    console.log(
      "✅ Statistiche salvate online con successo (pattern fetch diretto)"
    );

    // 🔄 INVALIDA CACHE per forzare ricaricamento del profilo
    invalidateProfileCache();
    console.log("🔄 Cache profilo invalidata dopo salvataggio online");
  } catch (error) {
    console.error("❌ ERRORE SALVATAGGIO ONLINE:", error);
    console.error("❌ Stack trace:", error.stack);
    console.error("❌ Tipo errore:", typeof error);
    console.error("❌ Messaggio:", error.message);

    // 🔄 AGGIUNGI ALLA CODA PER RETRY AUTOMATICO - Prova a ottenere utente
    try {
      const { data: userData } = await supabase.auth.getUser();
      if (userData?.user?.id) {
        const queueId = queueStatsUpdate(
          userData.user.id,
          updatedStats.stats,
          "high"
        );
        console.log(`📤 Statistiche aggiunte alla coda retry: ${queueId}`);

        // 🚨 FORZA SINCRONIZZAZIONE IMMEDIATA per problemi critici (con timeout ridotto)
        setTimeout(async () => {
          try {
            const { forceSyncStats } = await import("@/services/statsQueue");
            // Usa timeout più breve per evitare blocchi
            await Promise.race([
              forceSyncStats(),
              new Promise((_, reject) =>
                setTimeout(() => reject(new Error("Sync timeout")), 5000)
              ),
            ]);
            console.log("🔄 Sincronizzazione forzata completata");
          } catch (forceSyncError) {
            console.warn(
              "⚠️ Sincronizzazione forzata fallita:",
              forceSyncError
            );
          }
        }, 1000); // Ridotto a 1 secondo
      } else {
        console.warn(
          "⚠️ Impossibile aggiungere alla coda: nessun utente valido"
        );
      }
    } catch (queueError) {
      console.error("❌ Errore aggiunta alla coda:", queueError);
    }
  }

  return updatedStats;
};

// Aggiorna statistiche dopo un abbandono di partita
export const updateStatsAfterAbandonment = async (gameData: {
  difficulty: "easy" | "medium" | "hard";
  playerTeam: number;
  currentScore: [number, number];
}): Promise<{
  stats: PlayerStats;
  leveledUp: boolean;
  xpGained: number;
  xpBreakdown: string[];
}> => {
  return await updateStatsAfterGame({
    isWinner: false, // Abbandono = sempre sconfitta
    difficulty: gameData.difficulty,
    playerTeam: gameData.playerTeam,
    finalScore: gameData.currentScore,
    isAbandoned: true, // Marca come abbandono
  });
};

// 🔄 SINCRONIZZAZIONE BIDIREZIONALE AL LOGIN
export const syncStatsOnLogin = async (): Promise<{
  success: boolean;
  syncedStats?: PlayerStats;
  conflicts?: string[];
}> => {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return { success: false };
    }

    console.log("🔄 Iniziando sincronizzazione al login...");

    // 1. Leggi statistiche locali
    const localStats = getLocalPlayerStats();

    // 2. Leggi statistiche online
    const { data: onlineData, error } = await supabase
      .from("game_stats")
      .select("games_played, games_won, level, xp, updated_at")
      .eq("user_id", user.id as string)
      .single();

    if (error && error.code !== "PGRST116") {
      // PGRST116 = no rows found (nuovo utente)
      console.warn("Errore lettura stats online:", error);
      return { success: false };
    }

    const conflicts: string[] = [];
    let syncedStats: PlayerStats;

    if (!onlineData) {
      // Nuovo utente online - usa stats locali come base
      console.log("👤 Nuovo utente online - copiando stats locali");
      syncedStats = { ...localStats };
    } else {
      // Esiste profilo online - sincronizzazione bidirezionale
      // Cast per risolvere problemi di tipo TypeScript
      const onlineStats = onlineData as any;

      console.log("🔄 Sincronizzazione bidirezionale:", {
        local: {
          games: localStats.totalGames,
          won: localStats.gamesWon,
          level: localStats.level,
          xp: localStats.xp,
        },
        online: {
          games: onlineStats.games_played,
          won: onlineStats.games_won,
          level: onlineStats.level,
          xp: onlineStats.xp,
        },
      });

      // Per ogni campo, mantieni il valore massimo
      const syncedGamesPlayed = Math.max(
        localStats.totalGames,
        onlineStats.games_played || 0
      );
      const syncedGamesWon = Math.max(
        localStats.gamesWon,
        onlineStats.games_won || 0
      );
      const syncedLevel = Math.max(localStats.level, onlineStats.level || 1);
      const syncedXp = Math.max(localStats.xp, onlineStats.xp || 0);

      // Traccia conflitti risolti
      if (
        localStats.totalGames !== syncedGamesPlayed ||
        onlineStats.games_played !== syncedGamesPlayed
      ) {
        conflicts.push(
          `Partite giocate: ${localStats.totalGames} (locale) vs ${onlineStats.games_played} (online) → ${syncedGamesPlayed}`
        );
      }
      if (
        localStats.gamesWon !== syncedGamesWon ||
        onlineStats.games_won !== syncedGamesWon
      ) {
        conflicts.push(
          `Partite vinte: ${localStats.gamesWon} (locale) vs ${onlineStats.games_won} (online) → ${syncedGamesWon}`
        );
      }
      if (
        localStats.level !== syncedLevel ||
        onlineStats.level !== syncedLevel
      ) {
        conflicts.push(
          `Livello: ${localStats.level} (locale) vs ${onlineStats.level} (online) → ${syncedLevel}`
        );
      }
      if (localStats.xp !== syncedXp || onlineStats.xp !== syncedXp) {
        conflicts.push(
          `XP: ${localStats.xp} (locale) vs ${onlineStats.xp} (online) → ${syncedXp}`
        );
      }

      // Crea stats sincronizzate
      syncedStats = {
        ...localStats,
        totalGames: syncedGamesPlayed,
        gamesWon: syncedGamesWon,
        gamesLost: syncedGamesPlayed - syncedGamesWon,
        level: syncedLevel,
        xp: syncedXp,
        winRate:
          syncedGamesPlayed > 0
            ? Math.round((syncedGamesWon / syncedGamesPlayed) * 100)
            : 0,
        updatedAt: new Date().toISOString(),
      };
    }

    // 3. Salva stats sincronizzate sia localmente che online
    updateLocalPlayerStats(syncedStats);

    // 4. Aggiorna online
    const { error: updateError } = await supabase.from("game_stats").upsert(
      {
        user_id: user.id as string,
        games_played: syncedStats.totalGames,
        games_won: syncedStats.gamesWon,
        level: syncedStats.level,
        xp: syncedStats.xp,
        updated_at: syncedStats.updatedAt,
      } as any,
      { onConflict: "user_id" }
    );

    if (updateError) {
      console.warn("Errore salvataggio stats sincronizzate:", updateError);
      return { success: false };
    }

    // 5. Aggiorna cache
    forceUpdateProfileCache(syncedStats);

    if (conflicts.length > 0) {
      console.log("🔒 Conflitti risolti durante sincronizzazione:", conflicts);
    }

    console.log("✅ Sincronizzazione completata:", {
      games: syncedStats.totalGames,
      won: syncedStats.gamesWon,
      level: syncedStats.level,
      xp: syncedStats.xp,
      conflicts: conflicts.length,
    });

    return {
      success: true,
      syncedStats,
      conflicts: conflicts.length > 0 ? conflicts : undefined,
    };
  } catch (error) {
    console.error("❌ Errore sincronizzazione al login:", error);
    return { success: false };
  }
};

// Funzioni di utilità per compatibilità rimossa - usa direttamente getActiveProfile

export const getAuthenticationStatus = async () => {
  try {
    const {
      data: { user },
    } = await getCurrentUser();
    return {
      isAuthenticated: !!user,
      user: user
        ? {
            id: user.id,
            email: user.email,
            name: user.user_metadata?.full_name || user.user_metadata?.name,
          }
        : null,
    };
  } catch (error) {
    return {
      isAuthenticated: false,
      user: null,
    };
  }
};

export const getInstantConnectionStatus = async () => {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    return {
      isOnline: !!user,
      hasCache: !!profileCache, // 🎯 CORRETTO: Mostra se abbiamo cache
      lastSync: cacheTimestamp,
    };
  } catch {
    return {
      isOnline: false,
      hasCache: false,
      lastSync: 0,
    };
  }
};

// Funzione per invalidare cache (ora usa la cache corretta)
export const invalidateAuthCache = () => {
  invalidateProfileCache();
  console.log("🔄 Cache profilo invalidata");
};

export const onProfileUpdate = (callback: (profile: ProfileData) => void) => {
  console.log("onProfileUpdate callback registrato");
};

// 🚨 FUNZIONE DI EMERGENZA: Recupera dati utente dalla cache
const getCachedUserData = (): any | null => {
  try {
    console.log("🔍 DEBUG: Ricerca dati utente in cache...");

    // 1. Cache del profilo
    const profileCache = localStorage.getItem("profile_cache");
    if (profileCache) {
      console.log("🔍 DEBUG: Controllo profile_cache...");
      const parsed = JSON.parse(profileCache);
      console.log("🔍 DEBUG: profile_cache content:", parsed);
      if (parsed.user_id) {
        console.log("🔍 DEBUG: Trovato user_id in profile_cache");
        return {
          id: parsed.user_id,
          email: parsed.email || "<EMAIL>",
        };
      }
    }

    // 2. Cache delle statistiche unificate
    const unifiedData = localStorage.getItem("unified_storage_data");
    if (unifiedData) {
      console.log("🔍 DEBUG: Controllo unified_storage_data...");
      const parsed = JSON.parse(unifiedData);
      console.log("🔍 DEBUG: unified_storage content:", parsed);
      if (parsed.onlineFeatures?.user?.id) {
        console.log("🔍 DEBUG: Trovato user in unified_storage");
        return parsed.onlineFeatures.user;
      }
    }

    // 3. Cache Supabase auth
    const supabaseAuth = localStorage.getItem(
      "sb-bzqlnxftjcfcrfopcjlf-auth-token"
    );
    if (supabaseAuth) {
      console.log("🔍 DEBUG: Controllo supabase auth token...");
      const parsed = JSON.parse(supabaseAuth);
      console.log("🔍 DEBUG: supabase auth content:", parsed);
      if (parsed.user?.id) {
        console.log("🔍 DEBUG: Trovato user in supabase auth");
        return parsed.user;
      }
    }

    // 4. Cerca in tutte le chiavi che contengono "auth" o "user"
    console.log("🔍 DEBUG: Ricerca in tutte le chiavi localStorage...");
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (
        key &&
        (key.includes("user") ||
          key.includes("auth") ||
          key.includes("supabase"))
      ) {
        try {
          const value = localStorage.getItem(key);
          if (value) {
            console.log(`🔍 DEBUG: Controllo chiave ${key}...`);
            const parsed = JSON.parse(value);

            // Cerca user.id o user_id in qualsiasi struttura
            if (parsed.user?.id) {
              console.log(`🔍 DEBUG: Trovato user.id in ${key}`);
              return parsed.user;
            }
            if (parsed.id) {
              console.log(`🔍 DEBUG: Trovato id in ${key}`);
              return {
                id: parsed.id,
                email: parsed.email || "<EMAIL>",
              };
            }
            if (parsed.user_id) {
              console.log(`🔍 DEBUG: Trovato user_id in ${key}`);
              return {
                id: parsed.user_id,
                email: parsed.email || "<EMAIL>",
              };
            }
          }
        } catch (error) {
          console.log(`🔍 DEBUG: Errore parsing ${key}:`, error);
        }
      }
    }

    console.log("❌ DEBUG: Nessun dato utente trovato in cache");
    return null;
  } catch (error) {
    console.error("❌ DEBUG: Errore recupero cache utente:", error);
    return null;
  }
};

// 🚨 FUNZIONE DI EMERGENZA: Forza salvataggio senza autenticazione
const forceSaveStatsWithoutAuth = async (stats: PlayerStats): Promise<void> => {
  console.log("🚨 FORCE SAVE: Tentativo salvataggio di emergenza");

  try {
    // Salva in una coda di emergenza per retry futuro
    const emergencyQueue = JSON.parse(
      localStorage.getItem("emergency_stats_queue") || "[]"
    );

    const emergencyUpdate = {
      id: `emergency_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      stats: stats,
      timestamp: Date.now(),
      attempts: 0,
    };

    emergencyQueue.push(emergencyUpdate);

    // Mantieni solo gli ultimi 20 aggiornamenti di emergenza
    if (emergencyQueue.length > 20) {
      emergencyQueue.splice(0, emergencyQueue.length - 20);
    }

    localStorage.setItem(
      "emergency_stats_queue",
      JSON.stringify(emergencyQueue)
    );

    console.log(
      `🚨 FORCE SAVE: Salvato in coda di emergenza: ${emergencyUpdate.id}`
    );

    // Prova un salvataggio diretto con fetch (bypass Supabase client)
    try {
      await attemptDirectDatabaseSave(stats);
    } catch (directError) {
      console.warn("🚨 FORCE SAVE: Salvataggio diretto fallito:", directError);
    }
  } catch (error) {
    console.error("🚨 FORCE SAVE: Errore salvataggio di emergenza:", error);
  }
};

// 🚨 TENTATIVO SALVATAGGIO DIRETTO CON FETCH
const attemptDirectDatabaseSave = async (stats: PlayerStats): Promise<void> => {
  console.log("🚨 DIRECT SAVE: Tentativo salvataggio diretto con fetch");

  // Questo è un tentativo estremo - probabilmente non funzionerà senza token valido
  // ma vale la pena provare
  const cachedUser = getCachedUserData();
  if (!cachedUser?.id) {
    throw new Error("Nessun user ID disponibile per salvataggio diretto");
  }

  // Nota: Questo probabilmente fallirà per mancanza di autenticazione
  // ma è un tentativo di ultima istanza
  console.log(
    "🚨 DIRECT SAVE: Salvataggio diretto non implementato completamente"
  );
  throw new Error("Salvataggio diretto non disponibile senza token valido");
};

export const refreshOnlineProfile = async () => {
  // Invalida cache per forzare il ricaricamento
  invalidateProfileCache();
  return await getActiveProfile();
};

// Pulisci le cache alla chiusura
if (typeof window !== "undefined") {
  window.addEventListener("beforeunload", () => {
    invalidateAuthCache();
  });

  // 🐛 DEBUG: Aggiungi funzioni di test globali (solo in development)
  if (process.env.NODE_ENV === "development") {
    // @ts-expect-error - Debug functions for development
    window.debugStats = {
      testVictory: async () => {
        const result = await updateStatsAfterGame({
          isWinner: true,
          difficulty: "medium",
          playerTeam: 0,
          finalScore: [11, 7],
          maraffeMade: 2,
          isPerfectGame: false,
          isComeback: false,
          isDominantWin: false,
          isAbandoned: false,
        });
        console.log("🎯 Test Vittoria:", result);
        return result;
      },
      testDefeat: async () => {
        const result = await updateStatsAfterGame({
          isWinner: false,
          difficulty: "medium",
          playerTeam: 0,
          finalScore: [7, 11],
          maraffeMade: 0,
          isPerfectGame: false,
          isComeback: false,
          isDominantWin: false,
          isAbandoned: false,
        });
        console.log("🎯 Test Sconfitta:", result);
        return result;
      },
      testAbandon: async () => {
        const result = await updateStatsAfterAbandonment({
          difficulty: "medium",
          playerTeam: 0,
          currentScore: [5, 3],
        });
        console.log("🎯 Test Abbandono:", result);
        return result;
      },
      getProfile: async () => {
        const profile = await getActiveProfile();
        console.log("📊 Profilo corrente:", profile);
        return profile;
      },
      clearCache: () => {
        invalidateProfileCache();
        console.log("🔄 Cache profilo pulita");
      },
      checkCache: () => {
        console.log("� Stato cache:", {
          hasCache: !!profileCache,
          timestamp: cacheTimestamp,
          userId: cacheUserId,
          isValid: isCacheValid(cacheUserId),
          age: cacheTimestamp
            ? Math.round((Date.now() - cacheTimestamp) / 1000)
            : 0,
        });
      },
      syncStats: async () => {
        const result = await syncStatsOnLogin();
        console.log("🔄 Sincronizzazione manuale:", result);
        return result;
      },

      // Test per verificare il problema delle statistiche online
      testStatsUpdate: async () => {
        console.log("🧪 TEST: Verifica aggiornamento statistiche online");

        try {
          const result = await updateStatsAfterGame({
            isWinner: false,
            difficulty: "easy",
            playerTeam: 0,
            finalScore: [5, 11],
            isAbandoned: true,
          });

          console.log("✅ TEST: Risultato aggiornamento:", result);
          return result;
        } catch (error) {
          console.error("❌ TEST: Errore aggiornamento:", error);
          return null;
        }
      },

      // Test per verificare lo stato della sessione
      testAuthState: async () => {
        console.log("🧪 TEST: Verifica stato autenticazione");

        try {
          const { data: sessionData } = await supabase.auth.getSession();
          const { data: userData } = await supabase.auth.getUser();

          const now = Math.floor(Date.now() / 1000);
          const expiresAt = sessionData?.session?.expires_at || 0;
          const minutesUntilExpiry = Math.round((expiresAt - now) / 60);

          console.log("📊 TEST: Stato autenticazione:", {
            hasSession: !!sessionData?.session,
            hasUser: !!sessionData?.session?.user,
            hasToken: !!sessionData?.session?.access_token,
            userId: sessionData?.session?.user?.id,
            expiresAt: expiresAt,
            minutesUntilExpiry: minutesUntilExpiry,
            userFromGetUser: !!userData?.user,
            userIdFromGetUser: userData?.user?.id,
          });

          return {
            session: sessionData?.session,
            user: userData?.user,
            minutesUntilExpiry,
          };
        } catch (error) {
          console.error("❌ TEST: Errore controllo auth:", error);
          return null;
        }
      },

      // Visualizza coda di emergenza
      showEmergencyQueue: () => {
        try {
          const queue = JSON.parse(
            localStorage.getItem("emergency_stats_queue") || "[]"
          );
          console.log("🚨 CODA DI EMERGENZA:", queue);
          console.log(`📊 Elementi in coda: ${queue.length}`);
          return queue;
        } catch (error) {
          console.error("❌ Errore lettura coda emergenza:", error);
          return [];
        }
      },

      // Pulisci coda di emergenza
      clearEmergencyQueue: () => {
        try {
          localStorage.removeItem("emergency_stats_queue");
          console.log("🧹 Coda di emergenza pulita");
        } catch (error) {
          console.error("❌ Errore pulizia coda emergenza:", error);
        }
      },
    };

    console.log("🐛 Debug functions available at window.debugStats");
  }
}
