// 🚀 PROFILE SERVICE SEMPLIFICATO
// Sostituisce il vecchio profileService con logica più semplice e robusta

export * from "./newProfileService";

// Re-export per compatibilità con il codice esistente
export { 
  updateStatsAfterGame,
  getUserProfile as getActiveProfile,
  syncStatsOnLogin,
  invalidateProfileCache,
  getLocalStats as getPlayerStats,
  getCachedUserData,
  setCachedUserData,
} from "./newProfileService";

// Funzioni deprecate per compatibilità
export const updateStatsAfterAbandonment = async (gameData: {
  difficulty: "easy" | "medium" | "hard";
  playerTeam: number;
  currentScore: [number, number];
}) => {
  const { updateStatsAfterGame } = await import("./newProfileService");
  return updateStatsAfterGame({
    isWinner: false,
    difficulty: gameData.difficulty,
    playerTeam: gameData.playerTeam,
    finalScore: gameData.currentScore,
    isAbandoned: true,
  });
};

// Funzioni placeholder per compatibilità
export const invalidateAuthCache = () => {
  console.log("invalidateAuthCache chiamata - ora gestita automaticamente");
};

export const onProfileUpdate = (callback: any) => {
  console.log("onProfileUpdate callback registrato");
};

export const getLocalProfile = () => {
  const { getLocalStats } = require("./newProfileService");
  const stats = getLocalStats();
  return {
    stats,
    isOnline: false,
    userId: "local",
    username: "Giocatore Locale",
  };
};

// Debug functions per development
if (process.env.NODE_ENV === "development") {
  // @ts-expect-error - Debug functions for development
  window.debugStats = {
    testVictory: async () => {
      const { updateStatsAfterGame } = await import("./newProfileService");
      const result = await updateStatsAfterGame({
        isWinner: true,
        difficulty: "medium",
        playerTeam: 0,
        finalScore: [11, 7],
        isAbandoned: false,
      });
      console.log("🎯 Test Vittoria:", result);
      return result;
    },
    testDefeat: async () => {
      const { updateStatsAfterGame } = await import("./newProfileService");
      const result = await updateStatsAfterGame({
        isWinner: false,
        difficulty: "medium",
        playerTeam: 0,
        finalScore: [7, 11],
        isAbandoned: false,
      });
      console.log("🎯 Test Sconfitta:", result);
      return result;
    },
    testAbandon: async () => {
      const result = await updateStatsAfterAbandonment({
        difficulty: "medium",
        playerTeam: 0,
        currentScore: [5, 3],
      });
      console.log("🎯 Test Abbandono:", result);
      return result;
    },
    getProfile: async () => {
      const { getUserProfile } = await import("./newProfileService");
      const profile = await getUserProfile();
      console.log("📊 Profilo corrente:", profile);
      return profile;
    },
    clearCache: () => {
      const { invalidateProfileCache } = require("./newProfileService");
      invalidateProfileCache();
      console.log("🔄 Cache profilo pulita");
    },
    syncStats: async () => {
      const { syncStatsOnLogin } = await import("./newProfileService");
      const result = await syncStatsOnLogin();
      console.log("🔄 Sincronizzazione manuale:", result);
      return result;
    },
    testStatsUpdate: async () => {
      console.log("🧪 TEST: Verifica aggiornamento statistiche online");
      try {
        const { updateStatsAfterGame } = await import("./newProfileService");
        const result = await updateStatsAfterGame({
          isWinner: false,
          difficulty: "easy",
          playerTeam: 0,
          finalScore: [5, 11],
          isAbandoned: true,
        });
        console.log("✅ TEST: Risultato aggiornamento:", result);
        return result;
      } catch (error) {
        console.error("❌ TEST: Errore aggiornamento:", error);
        return null;
      }
    },
    testAuthState: async () => {
      console.log("🧪 TEST: Verifica stato autenticazione");
      try {
        const { supabase } = await import("@/integrations/supabase/client");
        const { data: { user }, error } = await supabase.auth.getUser();
        console.log("👤 Utente:", user ? user.id : "Nessuno");
        console.log("❌ Errore:", error);
        return { user, error };
      } catch (error) {
        console.error("❌ TEST: Errore controllo auth:", error);
        return { user: null, error };
      }
    },
  };
}
