// Test script per verificare il funzionamento del sistema di statistiche
import { updateStatsAfterGame } from "./newProfileService";

// Simulazione di una partita vinta
const testVictoryGame = async () => {
  console.log("🎯 Test partita vinta...");

  const result = await updateStatsAfterGame({
    isWinner: true,
    difficulty: "medium",
    playerTeam: 0,
    finalScore: [11, 7],
    isAbandoned: false,
  });

  console.log("✅ Risultato partita vinta:", {
    xpGained: result.xpGained,
    leveledUp: result.leveledUp,
    newLevel: result.stats.level,
    totalGames: result.stats.totalGames,
    gamesWon: result.stats.gamesWon,
    xpBreakdown: result.xpBreakdown,
  });

  return result;
};

// Simulazione di una partita abbandonata
const testAbandonedGame = async () => {
  console.log("🎯 Test partita abbandonata...");

  const result = await updateStatsAfterGame({
    isWinner: false,
    difficulty: "medium",
    playerTeam: 0,
    finalScore: [5, 3],
    isAbandoned: true,
  });

  console.log("✅ Risultato partita abbandonata:", {
    xpGained: result.xpGained,
    leveledUp: result.leveledUp,
    newLevel: result.stats.level,
    totalGames: result.stats.totalGames,
    gamesWon: result.stats.gamesWon,
    xpBreakdown: result.xpBreakdown,
  });

  return result;
};

// Simulazione di una partita persa
const testDefeatGame = async () => {
  console.log("🎯 Test partita persa...");

  const result = await updateStatsAfterGame({
    isWinner: false,
    difficulty: "medium",
    playerTeam: 0,
    finalScore: [7, 11],
    isAbandoned: false,
  });

  console.log("✅ Risultato partita persa:", {
    xpGained: result.xpGained,
    leveledUp: result.leveledUp,
    newLevel: result.stats.level,
    totalGames: result.stats.totalGames,
    gamesWon: result.stats.gamesWon,
    xpBreakdown: result.xpBreakdown,
  });

  return result;
};

// Esegue tutti i test
export const runStatsTests = async () => {
  console.log("🚀 Inizio test sistema statistiche...");

  try {
    await testVictoryGame();
    await testAbandonedGame();
    await testDefeatGame();

    console.log("✅ Tutti i test completati con successo!");
  } catch (error) {
    console.error("❌ Errore nei test:", error);
  }
};
