import { supabase } from "@/integrations/supabase/client";
import type { PlayerStats } from "@/types/game";

/**
 * 🚀 SERVIZIO SEMPLIFICATO PER SALVATAGGIO STATISTICHE
 * Risolve i problemi di timeout e complessità del profileService
 */

// Coda di retry per salvataggi falliti
const retryQueue: Array<{
  stats: any;
  retryCount: number;
  timestamp: number;
}> = [];

/**
 * 🎯 Salva le statistiche online con strategia semplificata
 */
export const saveStatsOnline = async (stats: PlayerStats): Promise<boolean> => {
  try {
    console.log("🚀 SALVATAGGIO SEMPLIFICATO: Inizio");

    // 1. Controlla se l'utente è autenticato
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.log("❌ Nessun utente autenticato, salvo solo localmente");
      return false;
    }

    console.log("✅ Utente autenticato trovato:", user.id);

    // 2. Prepara i dati per il salvataggio
    const statsToSave = {
      user_id: user.id,
      games_played: stats.totalGames,
      games_won: stats.gamesWon,
      level: stats.level,
      xp: stats.xp,
      updated_at: new Date().toISOString(),
    };

    console.log("📤 Invio statistiche al database:", statsToSave);

    // 3. Salva con upsert (inserisce o aggiorna)
    const { error: saveError } = await supabase
      .from("game_stats")
      .upsert(statsToSave, {
        onConflict: "user_id",
      });

    if (saveError) {
      console.error("❌ Errore salvataggio online:", saveError);
      addToRetryQueue(statsToSave);
      return false;
    }

    console.log("✅ Statistiche salvate online con successo!");
    return true;

  } catch (error) {
    console.error("❌ Errore generale salvataggio online:", error);
    
    // Aggiungi alla coda di retry
    const statsToSave = {
      user_id: "unknown",
      games_played: stats.totalGames,
      games_won: stats.gamesWon,
      level: stats.level,
      xp: stats.xp,
      updated_at: new Date().toISOString(),
    };
    addToRetryQueue(statsToSave);
    return false;
  }
};

/**
 * 🔄 Aggiunge statistiche alla coda di retry
 */
const addToRetryQueue = (stats: any) => {
  retryQueue.push({
    stats,
    retryCount: 0,
    timestamp: Date.now(),
  });
  
  console.log("📝 Aggiunto alla coda di retry:", stats);
  
  // Prova subito un retry dopo 5 secondi
  setTimeout(processRetryQueue, 5000);
};

/**
 * 🔄 Processa la coda di retry
 */
const processRetryQueue = async () => {
  if (retryQueue.length === 0) return;

  console.log("🔄 Processando coda di retry...");
  
  const item = retryQueue.shift();
  if (!item) return;

  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.log("❌ Retry fallito: nessun utente");
      return;
    }

    const statsToSave = {
      ...item.stats,
      user_id: user.id,
    };

    const { error } = await supabase
      .from("game_stats")
      .upsert(statsToSave, { onConflict: "user_id" });

    if (error) {
      console.error("❌ Retry fallito:", error);
      if (item.retryCount < 3) {
        item.retryCount++;
        retryQueue.push(item);
        console.log(`🔄 Riprovo tra 10 secondi (tentativo ${item.retryCount + 1}/3)`);
        setTimeout(processRetryQueue, 10000);
      } else {
        console.log("❌ Retry abbandonato dopo 3 tentativi");
      }
    } else {
      console.log("✅ Retry riuscito!");
    }
  } catch (error) {
    console.error("❌ Errore durante retry:", error);
    if (item.retryCount < 3) {
      item.retryCount++;
      retryQueue.push(item);
      setTimeout(processRetryQueue, 10000);
    }
  }
};

/**
 * 🔄 Forza il processamento di tutti i retry in coda
 */
export const forceProcessRetryQueue = async (): Promise<void> => {
  console.log("🔄 Forzando processamento coda retry...");
  while (retryQueue.length > 0) {
    await processRetryQueue();
    // Piccola pausa tra i tentativi
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
};

/**
 * 📊 Ottieni statistiche online dell'utente
 */
export const getOnlineStats = async (): Promise<PlayerStats | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return null;

    const { data, error } = await supabase
      .from("game_stats")
      .select("games_played, games_won, level, xp")
      .eq("user_id", user.id)
      .single();

    if (error) {
      console.error("❌ Errore lettura statistiche online:", error);
      return null;
    }

    return {
      totalGames: data.games_played || 0,
      gamesWon: data.games_won || 0,
      level: data.level || 1,
      xp: data.xp || 0,
    };
  } catch (error) {
    console.error("❌ Errore generale lettura statistiche:", error);
    return null;
  }
};

/**
 * 🔄 Sincronizza statistiche locali con quelle online
 */
export const syncStats = async (localStats: PlayerStats): Promise<PlayerStats> => {
  try {
    const onlineStats = await getOnlineStats();
    if (!onlineStats) {
      // Se non ci sono statistiche online, salva quelle locali
      await saveStatsOnline(localStats);
      return localStats;
    }

    // Prendi il massimo tra locale e online per ogni statistica
    const syncedStats: PlayerStats = {
      totalGames: Math.max(localStats.totalGames, onlineStats.totalGames),
      gamesWon: Math.max(localStats.gamesWon, onlineStats.gamesWon),
      level: Math.max(localStats.level, onlineStats.level),
      xp: Math.max(localStats.xp, onlineStats.xp),
    };

    // Salva le statistiche sincronizzate
    await saveStatsOnline(syncedStats);
    
    // Aggiorna anche il localStorage
    localStorage.setItem("playerStats", JSON.stringify(syncedStats));
    
    console.log("✅ Sincronizzazione completata:", syncedStats);
    return syncedStats;

  } catch (error) {
    console.error("❌ Errore sincronizzazione:", error);
    return localStats;
  }
};
