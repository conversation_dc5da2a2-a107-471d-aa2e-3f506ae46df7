import { supabase } from "@/integrations/supabase/client";
import { saveStatsOnline, syncStats } from "@/services/simpleStatsService";
import type { PlayerStats } from "@/types/game";

/**
 * 🚀 NUOVO SERVIZIO PROFILO SEMPLIFICATO
 * Sostituisce il vecchio profileService con logica più semplice e robusta
 */

export interface ProfileData {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  stats: PlayerStats;
  isOnline: boolean;
}

// Cache del profilo
let profileCache: ProfileData | null = null;

/**
 * 🎯 Aggiorna le statistiche dopo una partita
 */
export const updateStatsAfterGame = async (gameData: {
  isWinner: boolean;
  difficulty: "easy" | "medium" | "hard";
  playerTeam: number;
  finalScore: [number, number];
  isAbandoned?: boolean;
}): Promise<{
  stats: PlayerStats;
  leveledUp: boolean;
  xpGained: number;
  xpBreakdown: string[];
}> => {
  console.log("🎯 Aggiornamento statistiche:", gameData);

  // 1. Leggi statistiche locali
  const localStatsStr = localStorage.getItem("playerStats");
  const currentStats: PlayerStats = localStatsStr
    ? JSON.parse(localStatsStr)
    : {
        totalGames: 0,
        gamesWon: 0,
        level: 1,
        xp: 0,
      };

  // 2. Calcola XP guadagnato
  let xpGained = 0;
  const xpBreakdown: string[] = [];

  if (!gameData.isAbandoned) {
    // XP base per aver completato la partita
    xpGained += 10;
    xpBreakdown.push("Partita completata: +10 XP");

    // XP per vittoria
    if (gameData.isWinner) {
      xpGained += 25;
      xpBreakdown.push("Vittoria: +25 XP");
    }

    // Bonus difficoltà
    const difficultyBonus = {
      easy: 0,
      medium: 5,
      hard: 10,
    };
    const bonus = difficultyBonus[gameData.difficulty];
    if (bonus > 0) {
      xpGained += bonus;
      xpBreakdown.push(`Difficoltà ${gameData.difficulty}: +${bonus} XP`);
    }
  }

  // 3. Aggiorna statistiche
  const newStats: PlayerStats = {
    totalGames: currentStats.totalGames + 1,
    gamesWon: currentStats.gamesWon + (gameData.isWinner ? 1 : 0),
    level: currentStats.level,
    xp: currentStats.xp + xpGained,
  };

  // 4. Calcola nuovo livello
  const oldLevel = newStats.level;
  while (newStats.xp >= getXpForLevel(newStats.level + 1)) {
    newStats.level++;
  }
  const leveledUp = newStats.level > oldLevel;

  // 5. Salva localmente
  localStorage.setItem("playerStats", JSON.stringify(newStats));

  // 6. Aggiorna cache
  if (profileCache) {
    profileCache.stats = newStats;
  }

  // 7. Invia evento di aggiornamento
  window.dispatchEvent(new CustomEvent("statsUpdated"));

  // 8. Salva online (asincrono)
  saveStatsOnline(newStats).catch(error => {
    console.error("❌ Errore salvataggio online:", error);
  });

  console.log("📊 Statistiche aggiornate:", {
    level: newStats.level,
    xp: newStats.xp,
    totalGames: newStats.totalGames,
    gamesWon: newStats.gamesWon,
    xpGained,
  });

  return {
    stats: newStats,
    leveledUp,
    xpGained,
    xpBreakdown,
  };
};

/**
 * 📊 Calcola XP necessario per un livello
 */
const getXpForLevel = (level: number): number => {
  return level * 100; // 100 XP per livello
};

/**
 * 👤 Ottieni profilo utente
 */
export const getUserProfile = async (): Promise<ProfileData | null> => {
  try {
    // Controlla cache
    if (profileCache) {
      return profileCache;
    }

    // Ottieni utente autenticato
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return null;
    }

    // Leggi statistiche locali
    const localStatsStr = localStorage.getItem("playerStats");
    const localStats: PlayerStats = localStatsStr
      ? JSON.parse(localStatsStr)
      : {
          totalGames: 0,
          gamesWon: 0,
          level: 1,
          xp: 0,
        };

    // Sincronizza con online
    const syncedStats = await syncStats(localStats);

    // Crea profilo
    const profile: ProfileData = {
      id: user.id,
      email: user.email || "",
      name: user.user_metadata?.name || user.email || "Giocatore",
      avatar_url: user.user_metadata?.avatar_url,
      stats: syncedStats,
      isOnline: true,
    };

    // Aggiorna cache
    profileCache = profile;

    return profile;
  } catch (error) {
    console.error("❌ Errore caricamento profilo:", error);
    return null;
  }
};

/**
 * 🔄 Invalida cache profilo
 */
export const invalidateProfileCache = (): void => {
  profileCache = null;
  console.log("🔄 Cache profilo invalidata");
};

/**
 * 🔄 Sincronizza statistiche al login
 */
export const syncStatsOnLogin = async (): Promise<void> => {
  try {
    console.log("🔄 Sincronizzazione al login...");

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.log("❌ Nessun utente per sincronizzazione");
      return;
    }

    // Leggi statistiche locali
    const localStatsStr = localStorage.getItem("playerStats");
    const localStats: PlayerStats = localStatsStr
      ? JSON.parse(localStatsStr)
      : {
          totalGames: 0,
          gamesWon: 0,
          level: 1,
          xp: 0,
        };

    // Sincronizza
    const syncedStats = await syncStats(localStats);

    // Aggiorna cache se esiste
    if (profileCache) {
      profileCache.stats = syncedStats;
    }

    console.log("✅ Sincronizzazione completata:", syncedStats);
  } catch (error) {
    console.error("❌ Errore sincronizzazione al login:", error);
  }
};

/**
 * 📊 Ottieni statistiche locali
 */
export const getLocalStats = (): PlayerStats => {
  const localStatsStr = localStorage.getItem("playerStats");
  return localStatsStr
    ? JSON.parse(localStatsStr)
    : {
        totalGames: 0,
        gamesWon: 0,
        level: 1,
        xp: 0,
      };
};

/**
 * 🎯 Ottieni dati utente cached
 */
export const getCachedUserData = (): any => {
  try {
    const cached = localStorage.getItem("cachedUserData");
    return cached ? JSON.parse(cached) : null;
  } catch {
    return null;
  }
};

/**
 * 💾 Salva dati utente in cache
 */
export const setCachedUserData = (userData: any): void => {
  try {
    localStorage.setItem("cachedUserData", JSON.stringify(userData));
  } catch (error) {
    console.error("❌ Errore salvataggio cache utente:", error);
  }
};

// Callback placeholder per compatibilità
export const onProfileUpdate = (_callback: (profile: ProfileData) => void) => {
  console.log("onProfileUpdate callback registrato");
};
