import { supabase } from "@/integrations/supabase/client";
import { Capacitor } from "@capacitor/core";

export interface AuthUser {
  id: string;
  email?: string;
  name?: string;
  provider?: string;
}

// Google Auth nativo rimosso - usiamo solo autenticazione browser

// Funzione per login con Google migliorata per Android con autenticazione nativa
export const signInWithGoogle = async () => {
  try {
    // 🌐 AUTENTICAZIONE SOLO TRAMITE BROWSER
    // Supabase supporta solo autenticazione web, quindi usiamo direttamente il browser
    console.log("🌐 Avvio autenticazione Google tramite browser...");

    // Autenticazione tramite browser
    let redirectTo: string;

    if (Capacitor.isNativePlatform()) {
      redirectTo = "com.eliazavatta.maraffa://account";
      console.log("🚀 Login Google su mobile con redirect:", redirectTo);

      // Su Android, mostra un messaggio di caricamento
      if (Capacitor.getPlatform() === "android") {
        // Crea un overlay di caricamento
        const loadingOverlay = document.createElement("div");
        loadingOverlay.id = "google-auth-loading";
        loadingOverlay.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          z-index: 10000;
          color: white;
          font-family: Arial, sans-serif;
        `;
        loadingOverlay.innerHTML = `
          <div style="text-align: center;">
            <div style="width: 50px; height: 50px; border: 3px solid #f3f3f3; border-top: 3px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <h3 style="margin: 0 0 10px 0;">Accesso con Google</h3>
            <p style="margin: 0; opacity: 0.8;">Apertura browser per autenticazione...</p>
            <p style="margin: 10px 0 0 0; opacity: 0.6; font-size: 12px;">Torna all'app dopo aver effettuato l'accesso</p>
          </div>
          <style>
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          </style>
        `;
        document.body.appendChild(loadingOverlay);

        // Rimuovi l'overlay dopo 10 secondi o quando l'app torna in primo piano
        setTimeout(() => {
          const overlay = document.getElementById("google-auth-loading");
          if (overlay) overlay.remove();
        }, 10000);
      }
    } else {
      redirectTo = window.location.href;
      console.log("🚀 Login Google su web:", redirectTo);
    }

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: "google",
      options: {
        redirectTo,
        queryParams: {
          access_type: "offline",
          prompt: "select_account", // Forza la selezione dell'account
        },
        ...(Capacitor.isNativePlatform() && {
          skipBrowserRedirect: false,
        }),
      },
    });

    if (error) {
      console.error("❌ Errore login Google:", error);
      // Rimuovi overlay in caso di errore
      const overlay = document.getElementById("google-auth-loading");
      if (overlay) overlay.remove();
      throw error;
    }

    console.log("✅ Login Google avviato:", data);

    // Su mobile, avvia polling per rilevare il ritorno
    if (Capacitor.isNativePlatform()) {
      setTimeout(() => {
        waitForOAuthSession();
      }, 2000);
    }

    return { data, error: null };
  } catch (error: unknown) {
    console.error("❌ Errore login Google:", error);
    // Rimuovi overlay in caso di errore
    const overlay = document.getElementById("google-auth-loading");
    if (overlay) overlay.remove();
    return { data: null, error: (error as Error).message };
  }
};

// Funzione per login con Facebook
export const signInWithFacebook = async () => {
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: "facebook",
      options: {
        redirectTo: window.location.href,
        scopes: "email",
      },
    });

    if (error) throw error;
    return { data, error: null };
  } catch (error: unknown) {
    console.error("Errore login Facebook:", error);
    return { data: null, error: (error as Error).message };
  }
};

// Funzione per logout
export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;

    // 🎯 AGGIORNA MODALITÀ OFFLINE DOPO LOGOUT
    const { saveUnifiedData } = await import("./unifiedStorageService");
    saveUnifiedData({
      isOfflineMode: true, // L'utente è offline dopo logout
    });
    console.log("🔄 Modalità offline impostata dopo logout");

    return { error: null };
  } catch (error: unknown) {
    console.error("Errore logout:", error);
    return { error: (error as Error).message };
  }
};

// Funzione per ottenere l'utente corrente
export const getCurrentUser = () => {
  return supabase.auth.getUser();
};

// Funzione per ascoltare i cambiamenti di stato dell'autenticazione
export const onAuthStateChange = (
  callback: (user: AuthUser | null) => void
) => {
  return supabase.auth.onAuthStateChange((_event, session) => {
    if (session?.user) {
      const user: AuthUser = {
        id: session.user.id,
        email: session.user.email,
        name:
          session.user.user_metadata?.full_name ||
          session.user.user_metadata?.name,
        provider: session.user.app_metadata?.provider,
      };
      callback(user);
    } else {
      callback(null);
    }
  });
};

// Tipi per le statistiche del giocatore
export interface PlayerStats {
  level: number;
  xp: number;
  gamesPlayed: number;
  gamesWon: number;
  totalPoints: number;
}

// Funzione per sincronizzare il profilo online al login
export const syncPlayerProfile = async (username: string) => {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error("Utente non autenticato");
    }

    console.log("🔄 Sincronizzazione profilo per:", username);

    const { getUserProfile, invalidateProfileCache } = await import(
      "./profileService"
    );

    invalidateProfileCache();
    const profile = await getUserProfile();

    console.log("✅ Profilo sincronizzato:", profile);

    return {
      data: { message: "Profilo sincronizzato con successo" },
      error: null,
    };
  } catch (error: unknown) {
    console.error("Errore sincronizzazione profilo:", error);
    return { data: null, error: (error as Error).message };
  }
};

// Funzione per ottenere il profilo online
export const getOnlineProfile = async () => {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error("Utente non autenticato");
    }

    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", user.id)
      .single();

    if (error && error.code !== "PGRST116") {
      throw error;
    }

    return { data, error: null };
  } catch (error: unknown) {
    console.error("Errore recupero profilo online:", error);
    return { data: null, error: (error as Error).message };
  }
};

// Funzione per verificare se i provider OAuth sono configurati
export const checkOAuthProviders = async () => {
  try {
    await supabase.auth.signInWithOAuth({
      provider: "google",
      options: {
        redirectTo: `${window.location.origin}/account`,
        queryParams: {
          access_type: "offline",
          prompt: "consent",
        },
      },
    });

    return { available: true, error: null };
  } catch (error: unknown) {
    const err = error as { message?: string };
    if (err.message?.includes("provider is not enabled")) {
      return {
        available: false,
        error:
          "Provider OAuth non configurato in Supabase. Controlla la documentazione OAUTH_SETUP.md",
      };
    }
    return { available: false, error: err.message || "Errore sconosciuto" };
  }
};

// Funzione per attendere che la sessione OAuth sia disponibile (solo per mobile)
export const waitForOAuthSession = async (
  maxAttempts = 20,
  intervalMs = 1000
): Promise<{ session: object | null; user: AuthUser | null }> => {
  if (!Capacitor.isNativePlatform()) {
    return { session: null, user: null };
  }

  // Rimuovi overlay di caricamento quando inizia il polling
  const removeLoadingOverlay = () => {
    const overlay = document.getElementById("google-auth-loading");
    if (overlay) {
      overlay.remove();
      console.log("🔄 Overlay di caricamento rimosso");
    }
  };

  console.log(`🔄 Polling OAuth per ${maxAttempts} tentativi...`);

  for (let i = 0; i < maxAttempts; i++) {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (session && user) {
      console.log("✅ Sessione OAuth trovata!");
      removeLoadingOverlay(); // Rimuovi overlay quando login completato
      return {
        session,
        user: {
          id: user.id,
          email: user.email,
          name: user.user_metadata?.full_name || user.user_metadata?.name,
          provider: user.app_metadata?.provider,
        },
      };
    }

    if (i < maxAttempts - 1) {
      await new Promise((resolve) => setTimeout(resolve, intervalMs));
    }
  }

  console.warn("⚠️ Timeout OAuth polling");
  removeLoadingOverlay(); // Rimuovi overlay anche in caso di timeout
  return { session: null, user: null };
};

// Funzione semplificata per ottenere la sessione corrente - LASCIA CHE SUPABASE GESTISCA TUTTO
export const getValidSession = async () => {
  try {
    const {
      data: { session },
      error,
    } = await supabase.auth.getSession();

    if (error) {
      console.error("❌ Errore recupero sessione:", error);
      return null;
    }

    if (session?.access_token) {
      console.log("✅ Sessione valida trovata");
      return session;
    }

    console.warn("⚠️ Nessuna sessione valida");
    return null;
  } catch (error) {
    console.error("❌ Errore getValidSession:", error);
    return null;
  }
};

// Funzione semplificata per inizializzare i listener OAuth (solo per mobile)
export const initializeOAuthListeners = () => {
  if (!Capacitor.isNativePlatform()) return;

  console.log("🔧 Inizializzazione listener OAuth per mobile...");

  // Listener semplificato per quando l'app diventa visibile dopo OAuth
  document.addEventListener("visibilitychange", async () => {
    if (!document.hidden) {
      console.log("👁️ App visibile - controllo sessione...");

      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (session && session.user) {
        console.log("✅ Sessione trovata al ritorno");
        // Rimuovi overlay se presente
        const overlay = document.getElementById("google-auth-loading");
        if (overlay) {
          overlay.remove();
          console.log("🔄 Overlay rimosso al ritorno dall'auth");
        }
        // Trigghera l'onAuthStateChange automaticamente
      }
    }
  });
};
