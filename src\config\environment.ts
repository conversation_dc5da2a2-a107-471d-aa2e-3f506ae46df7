/**
 * 🔐 CONFIGURAZIONE CENTRALIZZATA AMBIENTE
 * Gestisce tutte le variabili d'ambiente in modo sicuro e tipizzato
 */

// Interfaccia per la configurazione tipizzata
interface EnvironmentConfig {
  // Supabase
  supabase: {
    url: string;
    anonKey: string;
  };

  // AdMob
  admob: {
    appId: string;
    bannerUnitId: string;
    iosAppId: string;
    iosBannerUnitId: string;
  };

  // App
  app: {
    id: string;
    name: string;
    version: string;
  };

  // OAuth
  oauth: {
    redirectUrlMobile: string;
    redirectUrlWeb: string;
    googleWebClientId: string;
    googleAndroidClientId: string;
  };

  // Feature Flags
  features: {
    debugShowAllCards: boolean;
    passPremium: boolean;
    enableAnalytics: boolean;
    enableCrashReporting: boolean;
    enableLogging: boolean;
    enableAdMobBanner: boolean; // Toggle per banner AdMob
  };

  // Security
  security: {
    apiTimeout: number;
    maxRetryAttempts: number;
    sessionRefreshTimeout: number;
    connectionHealthTimeout: number;
  };
}

/**
 * Funzione helper per ottenere variabili d'ambiente con fallback
 */
const getEnvVar = (key: string, defaultValue?: string): string => {
  const value = import.meta.env[key];
  if (!value && !defaultValue) {
    console.warn(`⚠️ Variabile d'ambiente mancante: ${key}`);
    return "";
  }
  return value || defaultValue || "";
};

/**
 * Funzione helper per ottenere boolean da variabili d'ambiente
 */
const getEnvBoolean = (key: string, defaultValue: boolean = false): boolean => {
  const value = import.meta.env[key];
  if (value === undefined) return defaultValue;
  return value === "true";
};

/**
 * Funzione helper per ottenere numeri da variabili d'ambiente
 */
const getEnvNumber = (key: string, defaultValue: number): number => {
  const value = import.meta.env[key];
  if (value === undefined) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
};

/**
 * Configurazione centralizzata dell'ambiente
 */
export const env: EnvironmentConfig = {
  supabase: {
    url: getEnvVar(
      "VITE_SUPABASE_URL",
      "https://bzqlnxftjcfcrfopcjlf.supabase.co"
    ),
    anonKey: getEnvVar("VITE_SUPABASE_ANON_KEY"),
  },

  admob: {
    appId: getEnvVar(
      "VITE_ADMOB_APP_ID",
      "ca-app-pub-****************~1249225182"
    ),
    bannerUnitId: getEnvVar(
      "VITE_ADMOB_BANNER_UNIT_ID",
      "ca-app-pub-****************/**********"
    ),
    iosAppId: getEnvVar(
      "VITE_ADMOB_IOS_APP_ID",
      "ca-app-pub-****************~1249225182" // Placeholder - da sostituire con ID iOS reale
    ),
    iosBannerUnitId: getEnvVar(
      "VITE_ADMOB_IOS_BANNER_UNIT_ID",
      "ca-app-pub-****************/**********" // Placeholder - da sostituire con ID iOS reale
    ),
  },

  app: {
    id: getEnvVar("VITE_APP_ID", "com.eliazavatta.maraffa"),
    name: getEnvVar("VITE_APP_NAME", "Marafone Romagnolo"),
    version: getEnvVar("VITE_APP_VERSION", "1.0.0"),
  },

  oauth: {
    redirectUrlMobile: getEnvVar(
      "VITE_OAUTH_REDIRECT_URL_MOBILE",
      "com.eliazavatta.maraffa://account"
    ),
    redirectUrlWeb: getEnvVar(
      "VITE_OAUTH_REDIRECT_URL_WEB",
      `${window.location.origin}/account`
    ),
    googleWebClientId: getEnvVar(
      "VITE_GOOGLE_WEB_CLIENT_ID",
      "************-hp0fcggd5arp93kcq761ajio55vp5osn.apps.googleusercontent.com"
    ),
    googleAndroidClientId: getEnvVar(
      "VITE_GOOGLE_ANDROID_CLIENT_ID",
      "************-mk054mc350iino69ouik0s7bk6nqpur7.apps.googleusercontent.com"
    ),
  },

  features: {
    debugShowAllCards: getEnvBoolean("VITE_DEBUG_SHOW_ALL_CARDS", false),
    passPremium: getEnvBoolean("VITE_PASS_PREMIUM", false),
    enableAnalytics: getEnvBoolean("VITE_ENABLE_ANALYTICS", true),
    enableCrashReporting: getEnvBoolean("VITE_ENABLE_CRASH_REPORTING", true),
    enableLogging: getEnvBoolean("VITE_ENABLE_LOGGING", false),
    enableAdMobBanner: getEnvBoolean("VITE_ENABLE_ADMOB_BANNER", false), // Temporaneamente disabilitato
  },

  security: {
    apiTimeout: getEnvNumber("VITE_API_TIMEOUT", 60000), // Aumentato a 60 secondi per app mobile
    maxRetryAttempts: getEnvNumber("VITE_MAX_RETRY_ATTEMPTS", 5), // Aumentato per maggiore resilienza
    sessionRefreshTimeout: getEnvNumber("VITE_SESSION_REFRESH_TIMEOUT", 120000), // 2 minuti per refresh sessione
    connectionHealthTimeout: getEnvNumber(
      "VITE_CONNECTION_HEALTH_TIMEOUT",
      30000
    ), // 30 secondi per controlli salute
  },
};

/**
 * Validazione configurazione all'avvio
 */
export const validateEnvironment = (): void => {
  const errors: string[] = [];

  // Validazioni critiche
  if (!env.supabase.url) {
    errors.push("VITE_SUPABASE_URL è richiesto");
  }

  if (!env.supabase.anonKey) {
    errors.push("VITE_SUPABASE_ANON_KEY è richiesto");
  }

  // Validazioni formato
  if (env.supabase.url && !env.supabase.url.startsWith("https://")) {
    errors.push("VITE_SUPABASE_URL deve iniziare con https://");
  }

  if (env.security.apiTimeout < 1000) {
    errors.push("VITE_API_TIMEOUT deve essere almeno 1000ms");
  }

  if (errors.length > 0) {
    console.error("❌ Errori di configurazione ambiente:");
    errors.forEach((error) => console.error(`  - ${error}`));
    throw new Error("Configurazione ambiente non valida");
  }

  console.log("✅ Configurazione ambiente validata con successo");
};

/**
 * Informazioni ambiente per debug (senza dati sensibili)
 */
export const getEnvironmentInfo = () => ({
  mode: import.meta.env.MODE,
  dev: import.meta.env.DEV,
  prod: import.meta.env.PROD,
  features: env.features,
  app: env.app,
  security: {
    apiTimeout: env.security.apiTimeout,
    maxRetryAttempts: env.security.maxRetryAttempts,
    sessionRefreshTimeout: env.security.sessionRefreshTimeout,
    connectionHealthTimeout: env.security.connectionHealthTimeout,
  },
});

// Validazione automatica all'import
if (import.meta.env.PROD) {
  validateEnvironment();
}
