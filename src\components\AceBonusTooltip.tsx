import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Crown } from "lucide-react";

interface AceBonusTooltipProps {
  isVisible: boolean;
  onClose: () => void;
  aceCardPosition?: { x: number; y: number };
}

const AceBonusTooltip: React.FC<AceBonusTooltipProps> = ({
  isVisible,
  onClose,
  aceCardPosition,
}) => {
  return (
    <AnimatePresence>
      {isVisible && (
        <>
          {/* Backdrop overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/20 z-40 pointer-events-none"
          />

          {/* Tooltip */}
          <motion.div
            initial={{
              opacity: 0,
              scale: 0.8,
              y: 20,
            }}
            animate={{
              opacity: 1,
              scale: 1,
              y: 0,
            }}
            exit={{
              opacity: 0,
              scale: 0.8,
              y: 20,
            }}
            transition={{
              type: "spring",
              damping: 20,
              stiffness: 300,
            }}
            className="fixed z-50 pointer-events-auto"
            style={{
              left: aceCardPosition?.x ? `${aceCardPosition.x + 20}px` : "50%",
              top: aceCardPosition?.y ? `${aceCardPosition.y - 160}px` : "50%",
              transform: aceCardPosition
                ? "translateX(-50%)"
                : "translate(-50%, -50%)",
            }}
          >
            {/* Arrow pointing to the ace card */}
            {aceCardPosition && (
              <div className="absolute top-full left-1/2 transform -translate-x-1/2">
                <div className="w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-amber-500"></div>
              </div>
            )}

            {/* Tooltip content */}
            <div className="bg-gradient-to-br from-amber-400 via-yellow-400 to-amber-500 rounded-xl shadow-2xl border-2 border-amber-600 p-4 max-w-xs relative">
              {/* Close button */}
              <button
                onClick={onClose}
                className="absolute top-2 right-2 w-6 h-6 bg-amber-600 hover:bg-amber-700 rounded-full flex items-center justify-center transition-colors duration-200"
                aria-label="Chiudi tooltip"
              >
                <X size={12} className="text-white" />
              </button>

              {/* Content */}
              <div className="flex items-start gap-1">
                <div className="flex-1">
                  <h3 className="text-amber-900 font-bold text-sm mb-1">
                    Hai la <strong>maraffa</strong>
                  </h3>
                  <p className="text-amber-800 text-xs leading-relaxed">
                    (A-2-3 di briscola)!{" "}
                    <span className="font-bold text-amber-900">
                      Gioca l'Asso
                    </span>{" "}
                    per ottenere{" "}
                    <span className="font-bold text-green-700">
                      +3 punti bonus
                    </span>
                    !
                  </p>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute -top-1 -left-1 w-3 h-3 bg-yellow-300 rounded-full opacity-60"></div>
              <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-amber-600 rounded-full opacity-40"></div>
            </div>

            {/* Pulsing glow effect */}
            <motion.div
              animate={{
                scale: [1, 1.05, 1],
                opacity: [0.3, 0.6, 0.3],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              className="absolute inset-0 bg-amber-400 rounded-xl blur-md -z-10"
            />
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default AceBonusTooltip;
